#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::ditu_rizhi_kongzhi::ditu_rizhi_kongzhi;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use anyhow::Result;

/// 地图Redis缓存控制类
pub struct ditu_redis_kongzhi {
    redis_lianjie: redis_lianjie_guanli,
}

/// 地图缓存时间常量
impl ditu_redis_kongzhi {
    /// 地图全部数据缓存时间：3天（259200秒）
    pub const quanbu_shuju_huancun_shijian: i64 = 259200;
}

impl ditu_redis_kongzhi {
    /// 创建新的地图Redis控制实例
    pub fn new(redis_lianjie: redis_lianjie_guanli) -> Self {
        Self {
            redis_lianjie,
        }
    }

    /// 生成地图全部信息的Redis键
    pub fn shengcheng_quanbu_xinxi_jian(ditu_id: &str) -> String {
        format!("{}{}", ditu_rizhi_kongzhi::redis_ditu_jian_qianzhui, ditu_id)
    }

    /// 设置地图全部信息到Redis缓存
    pub async fn shezhi_ditu_quanbu_xinxi(&self, ditu_id: &str, shuju: &str) -> Result<()> {
        let redis_jian = Self::shengcheng_quanbu_xinxi_jian(ditu_id);
        match self.redis_lianjie.shezhi_with_guoqi(
            &redis_jian,
            shuju,
            Self::quanbu_shuju_huancun_shijian,
        ).await {
            Ok(()) => {
                ditu_rizhi_kongzhi::redis_shezhi_huancun_chenggong(ditu_id, Self::quanbu_shuju_huancun_shijian);
                Ok(())
            }
            Err(e) => {
                ditu_rizhi_kongzhi::redis_shezhi_huancun_shibai(ditu_id, &e.to_string());
                Err(e)
            }
        }
    }

    /// 获取地图全部信息从Redis缓存
    pub async fn huoqu_ditu_quanbu_xinxi(&self, ditu_id: &str) -> Result<Option<String>> {
        let redis_jian = Self::shengcheng_quanbu_xinxi_jian(ditu_id);
        match self.redis_lianjie.huoqu(&redis_jian).await {
            Ok(Some(shuju)) => {
                ditu_rizhi_kongzhi::redis_huancun_minzhong(ditu_id);
                Ok(Some(shuju))
            }
            Ok(None) => {
                ditu_rizhi_kongzhi::redis_huancun_wei_minzhong(ditu_id);
                Ok(None)
            }
            Err(e) => {
                ditu_rizhi_kongzhi::redis_huoqu_huancun_shibai(ditu_id, &e.to_string());
                Err(e)
            }
        }
    }

    /// 删除指定地图的全部信息缓存
    pub async fn shanchu_ditu_quanbu_xinxi(&self, ditu_id: &str) -> Result<bool> {
        let redis_jian = Self::shengcheng_quanbu_xinxi_jian(ditu_id);
        match self.redis_lianjie.shanchu(&redis_jian).await {
            Ok(shanchu_chenggong) => {
                if shanchu_chenggong {
                    ditu_rizhi_kongzhi::redis_shanchu_huancun_chenggong(ditu_id);
                }
                Ok(shanchu_chenggong)
            }
            Err(e) => {
                ditu_rizhi_kongzhi::redis_shanchu_huancun_shibai(ditu_id, &e.to_string());
                Err(e)
            }
        }
    }

    /// 检查指定地图的缓存是否存在
    pub async fn jiancha_ditu_huancun_cunzai(&self, ditu_id: &str) -> Result<bool> {
        let redis_jian = Self::shengcheng_quanbu_xinxi_jian(ditu_id);
        match self.redis_lianjie.cunzai(&redis_jian).await {
            Ok(cunzai) => {
                ditu_rizhi_kongzhi::redis_jiancha_huancun_cunzai(ditu_id, cunzai);
                Ok(cunzai)
            }
            Err(e) => {
                ditu_rizhi_kongzhi::redis_jiancha_huancun_shibai(ditu_id, &e.to_string());
                Err(e)
            }
        }
    }

    /// 清除所有地图相关的Redis缓存
    pub async fn qingchu_suoyou_ditu_huancun(&self) -> Result<u64> {
        let pattern = ditu_rizhi_kongzhi::redis_ditu_moshi_pipei;
        match self.redis_lianjie.shanchu_by_pattern(pattern).await {
            Ok(shuliang) => {
                ditu_rizhi_kongzhi::redis_piliang_qingchu_chenggong(shuliang);
                Ok(shuliang)
            }
            Err(e) => {
                ditu_rizhi_kongzhi::redis_piliang_qingchu_shibai(&e.to_string());
                Err(e)
            }
        }
    }

    /// 获取地图缓存统计信息
    pub async fn huoqu_ditu_huancun_tongji(&self) -> Result<String> {
        let pattern = ditu_rizhi_kongzhi::redis_ditu_moshi_pipei;
        match self.redis_lianjie.count_keys_by_pattern(pattern).await {
            Ok(count) => {
                let tongji_xinxi = format!("{}", ditu_rizhi_kongzhi::ditu_quanbu_xinxi_huancun_tongji_geshi.replace("{}", &count.to_string()));
                ditu_rizhi_kongzhi::redis_huoqu_tongji_chenggong(&tongji_xinxi);
                Ok(tongji_xinxi)
            }
            Err(e) => {
                let cuowu_xinxi = format!("{}", ditu_rizhi_kongzhi::huoqu_ditu_huancun_tongji_shibai_geshi.replace("{}", &e.to_string()));
                ditu_rizhi_kongzhi::redis_huoqu_tongji_shibai(&e.to_string());
                Ok(cuowu_xinxi)
            }
        }
    }
}